import React from 'react';
import EnhancedOnboardingTour from './EnhancedOnboardingTour';
import { useSimpleTour } from './useSimpleTour';

/**
 * 全局引导组件
 * 自动检测页面变化并触发相应引导
 * 只需要在根布局中引入一次即可
 */
const GlobalOnboarding: React.FC = () => {
  // 使用简化的引导Hook，自动处理所有逻辑
  const { currentPage } = useSimpleTour({
    delay: 1000, // 页面加载后1秒启动引导
  });

  return <EnhancedOnboardingTour page={currentPage} />;
};

export default GlobalOnboarding;
