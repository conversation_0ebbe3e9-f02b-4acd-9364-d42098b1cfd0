import { Step } from 'react-joyride';

/**
 * 优化的引导步骤配置
 * 使用更智能的选择器，减少对data-tour属性的依赖
 */

// 通用选择器工具函数
const createMenuSelector = (text: string) => `a[href*="${text}"], button:contains("${text}")`;
const createTextSelector = (text: string) => `:contains("${text}")`;

export const optimizedTourSteps: { [key: string]: Step[] } = {

  // 对话页面引导
  chat: [
    {
      target: 'textarea[placeholder*="输入"], input[placeholder*="输入"], [role="textbox"]',
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以换行。',
      placement: 'top',
      disableBeacon: true,
    },
    {
      target: 'button[type="submit"], button:contains("发送"), [aria-label*="发送"]',
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
    },
  ],

  // 智能体市场引导
  agent: [
    {
      target: '[data-tour="agent-categories"], .category-filter, .filter-tabs',
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="agent-card"], .agent-card, .module-card',
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
    },
  ],

  // 课程页面引导
  course: [
    {
      target: '[data-tour="course-categories"], .course-filter, .category-tabs',
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="course-card"], .course-card, .course-item',
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
    },
  ],

  // 工作流页面引导
  workflow: [
    {
      target: '[data-tour="agent-categories"], .workflow-filter, .category-filter',
      content: '浏览不同类别的工作流，找到适合您业务需求的自动化流程。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="agent-card"], .workflow-card, .module-card',
      content: '点击工作流卡片查看详情，了解自动化流程的功能和使用方法。',
      placement: 'top',
    },
  ],

  // 数字人页面引导
  digitalVideo: [
    {
      target: '[data-tour="digital-categories"], .digital-filter, .avatar-categories',
      content: '选择不同风格的数字人，为您的内容创作增添个性化元素。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="digital-card"], .digital-card, .avatar-card',
      content: '预览数字人效果，选择最适合您需求的虚拟形象。',
      placement: 'top',
    },
  ],

  // 知识库页面引导
  knowledge: [
    {
      target: '[data-tour="knowledge-upload"], .upload-button, button:contains("上传")',
      content: '上传您的文档、资料，构建个人专属知识库。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="knowledge-list"], .knowledge-list, .file-list',
      content: '管理您的知识库内容，支持搜索、编辑和删除操作。',
      placement: 'top',
    },
  ],

  // 个人中心引导
  ucenter: [
    {
      target: '[data-tour="ucenter-assets"]',
      content: '查看您购买的智能体和课程，管理您的数字资产。',
      placement: 'right',
      disableBeacon: true,
    },
    {
      target: '[data-tour="ucenter-usage"]',
      content: '查看您的使用情况和消费记录，了解服务使用详情。',
      placement: 'right',
    },
    {
      target: '[data-tour="ucenter-account"]',
      content: '管理您的账号信息，包括个人资料和安全设置。',
      placement: 'right',
    },
  ],
};

// 智能选择器匹配函数
export const findElementBySmartSelector = (selectors: string[]): Element | null => {
  for (const selector of selectors) {
    try {
      const element = document.querySelector(selector);
      if (element) return element;
    } catch (error) {
      console.warn(`Invalid selector: ${selector}`, error);
    }
  }
  return null;
};

// 动态步骤生成器
export const generateDynamicSteps = (pageType: string): Step[] => {
  const baseSteps = optimizedTourSteps[pageType] || [];
  
  return baseSteps.map(step => ({
    ...step,
    // 如果原始选择器找不到元素，尝试备用选择器
    target: step.target,
  }));
};

// 根据页面获取优化的引导步骤
export const getOptimizedStepsByPage = (page: string): Step[] => {
  return generateDynamicSteps(page);
};

// 兼容性函数，保持向后兼容
export const getStepsByPage = getOptimizedStepsByPage;

// 根据路径确定页面类型的辅助函数
export const getPageTypeFromPath = (pathname: string): string => {
  if (pathname === '/chat') return 'chat';
  if (pathname.startsWith('/agent/marketplace')) return 'agent';
  if (pathname.startsWith('/workflow/marketplace')) return 'workflow';
  if (pathname.startsWith('/course')) return 'course';
  if (pathname.startsWith('/digitalVideo')) return 'digitalVideo';
  if (pathname.startsWith('/knowledge')) return 'knowledge';
  if (pathname.startsWith('/ucenter')) return 'ucenter';
  // 其他路径不启动引导
  return '';
};
